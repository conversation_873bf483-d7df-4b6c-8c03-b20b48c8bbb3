<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspirational Quote | Wisdom Collection - quotese.com</title>
    <meta name="description" content="Explore this profound quote and its context. Discover wisdom, inspiration, and insights from history's most influential thinkers.">
    <meta name="keywords" content="inspirational quote, wisdom quote, famous saying, thought-provoking quote, life quote">
    <link rel="canonical" href="https://quotese.com/quotes/">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Inspirational Quote | Wisdom Collection - quotese.com">
    <meta property="og:description" content="Explore this profound quote and its context. Discover wisdom, inspiration, and insights from history's most influential thinkers.">
    <meta property="og:image" content="https://quotese.com/images/og-image-quote.jpg">
    <meta property="og:url" content="https://quotese.com/quotes/">
    <meta property="og:type" content="article">
    <meta property="og:site_name" content="quotese.com">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@quotesecom">
    <meta name="twitter:title" content="Inspirational Quote | Wisdom Collection">
    <meta name="twitter:description" content="Explore this profound quote and its context. Discover wisdom, inspiration, and insights from history's most influential thinkers.">
    <meta name="twitter:image" content="https://quotese.com/images/og-image-quote.jpg">
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif:wght@400;500;600;700&family=Noto+Sans:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
    <link href="/css/quote-detail.css" rel="stylesheet">
    <!-- Google Analytics -->
    <script src="/js/analytics.js?v=20250626"></script>
</head>
<body class="light-mode">
    <!-- 导航栏 (将由组件加载器加载) -->
    <header id="navigation-container" role="banner"></header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-8" role="main">
        <!-- 面包屑导航 -->
        <div id="breadcrumb-container"></div>

        <!-- 名言主要展示区域 (Hero Section) -->
        <section class="mb-12 fade-in" id="quote-hero-section">
            <div class="max-w-4xl mx-auto">
                <article class="quote-detail-card">
                    <!-- 名言内容 -->
                    <blockquote class="quote-content-area">
                        <h1 id="quote-content" class="quote-text">
                            "Quote content will be displayed here."
                        </h1>
                    </blockquote>

                    <!-- 作者和来源信息 -->
                    <div class="quote-attribution">
                        <div class="author-info">
                            <div class="author-avatar">
                                <span id="author-initial" class="text-lg font-bold">A</span>
                            </div>
                            <div class="author-details">
                                <a id="author-link" href="#" class="author-name">Author Name</a>
                                <p id="source-text" class="source-name">Source Name</p>
                            </div>
                        </div>
                    </div>

                    <!-- 分类标签 -->
                    <div class="categories-section" id="categories-container">
                        <!-- Categories will be added here -->
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="quote-actions">
                        <div class="quote-meta">
                            <span id="quote-date" class="text-sm text-gray-500 dark:text-gray-400">Date</span>
                        </div>
                        <div class="action-buttons">
                            <button id="share-button" class="action-btn share-btn" title="分享">
                                <i class="fas fa-share-alt"></i>
                                <span class="hidden sm:inline">分享</span>
                            </button>
                            <button id="copy-button" class="action-btn copy-btn" title="复制">
                                <i class="fas fa-copy"></i>
                                <span class="hidden sm:inline">复制</span>
                            </button>
                            <button id="favorite-button" class="action-btn favorite-btn" title="收藏">
                                <i class="far fa-heart"></i>
                                <span class="hidden sm:inline">收藏</span>
                            </button>
                        </div>
                    </div>
                </article>
            </div>
        </section>

        <!-- 内容网格布局 -->
        <div class="content-grid">
            <!-- 左栏：相关推荐 -->
            <section class="main-content">
                <div id="related-quotes-container">
                    <!-- 相关名言推荐组件 -->
                    <div class="flex justify-center py-12" id="related-quotes-loading">
                        <div class="loading-spinner" role="status">
                            <span class="sr-only">Loading related quotes...</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 右栏：侧边栏 -->
            <aside class="sidebar" role="complementary" aria-label="Sidebar">
                <div id="author-info-widget">
                    <!-- 作者信息扩展组件 -->
                </div>
                <div id="popular-topics-container">
                    <!-- 热门话题组件 -->
                </div>
            </aside>
        </div>
    </main>

    <!-- Footer (will be loaded by component loader) -->
    <footer id="footer-container" role="contentinfo"></footer>

    <!-- JavaScript -->
    <!-- Debug Script -->
    <script src="/js/debug.js?v=20250626"></script>

    <!-- Component Loader -->
    <script src="/js/component-loader.js?v=20250626"></script>

    <!-- Mock Data -->
    <script src="/js/mock-data.js?v=20250626"></script>

    <!-- API Client -->
    <script src="/js/api-client.js?v=20250626"></script>

    <!-- Core Modules -->
    <script src="/js/theme.js?v=20250626"></script>
    <script src="/js/url-handler.js?v=20250626"></script>
    <script src="/js/seo-manager.js?v=20250626"></script>
    <script src="/js/page-router.js?v=20250626"></script>
    <script src="/js/mobile-menu.js?v=20250626"></script>
    <script src="/js/mobile-gestures.js?v=20250628"></script>
    <script src="/js/components/breadcrumb.js?v=20250626"></script>
    <script src="/js/components/related-quotes.js?v=20250628"></script>
    <script src="/js/components/author-info-widget.js?v=20250628"></script>
    <script src="/js/social-meta.js?v=20250626"></script>

    <!-- Global Fix Script -->
    <script src="/js/global-fix.js?v=20250626"></script>

    <!-- Page Specific Script -->
    <script src="/js/pages/quote.js?v=20250626"></script>
</body>
</html>
