/**
 * 移动端手势支持
 * 为名言详情页添加移动端友好的手势交互
 *
 * @version 1.0.0
 * @date 2025-06-28
 * <AUTHOR>
 */

/**
 * 移动端手势管理器
 */
class MobileGestureManager {
    constructor() {
        this.startX = 0;
        this.startY = 0;
        this.endX = 0;
        this.endY = 0;
        this.minSwipeDistance = 50;
        this.maxVerticalDistance = 100;
        
        this.init();
    }

    /**
     * 初始化手势监听
     */
    init() {
        if (!this.isMobile()) return;
        
        document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
        
        // 添加移动端优化的CSS类
        document.body.classList.add('mobile-optimized');
        
        console.log('MobileGestureManager: Initialized for mobile device');
    }

    /**
     * 检测是否为移动设备
     */
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;
    }

    /**
     * 处理触摸开始
     */
    handleTouchStart(e) {
        if (e.touches.length !== 1) return;
        
        const touch = e.touches[0];
        this.startX = touch.clientX;
        this.startY = touch.clientY;
    }

    /**
     * 处理触摸结束
     */
    handleTouchEnd(e) {
        if (e.changedTouches.length !== 1) return;
        
        const touch = e.changedTouches[0];
        this.endX = touch.clientX;
        this.endY = touch.clientY;
        
        this.detectSwipe();
    }

    /**
     * 检测滑动手势
     */
    detectSwipe() {
        const deltaX = this.endX - this.startX;
        const deltaY = this.endY - this.startY;
        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);
        
        // 检查是否为有效的水平滑动
        if (absDeltaX > this.minSwipeDistance && absDeltaY < this.maxVerticalDistance) {
            if (deltaX > 0) {
                this.handleSwipeRight();
            } else {
                this.handleSwipeLeft();
            }
        }
        
        // 检查是否为有效的垂直滑动
        if (absDeltaY > this.minSwipeDistance && absDeltaX < this.maxVerticalDistance) {
            if (deltaY > 0) {
                this.handleSwipeDown();
            } else {
                this.handleSwipeUp();
            }
        }
    }

    /**
     * 处理右滑手势
     */
    handleSwipeRight() {
        console.log('MobileGestureManager: Swipe right detected');
        
        // 右滑返回上一页
        if (window.history.length > 1) {
            window.history.back();
        } else {
            // 如果没有历史记录，跳转到首页
            window.location.href = '/';
        }
    }

    /**
     * 处理左滑手势
     */
    handleSwipeLeft() {
        console.log('MobileGestureManager: Swipe left detected');
        
        // 左滑可以触发分享功能
        const shareBtn = document.getElementById('share-button');
        if (shareBtn) {
            shareBtn.click();
        }
    }

    /**
     * 处理上滑手势
     */
    handleSwipeUp() {
        console.log('MobileGestureManager: Swipe up detected');
        
        // 上滑滚动到相关推荐区域
        const relatedSection = document.getElementById('related-quotes-container');
        if (relatedSection) {
            relatedSection.scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
            });
        }
    }

    /**
     * 处理下滑手势
     */
    handleSwipeDown() {
        console.log('MobileGestureManager: Swipe down detected');
        
        // 下滑滚动到页面顶部
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    /**
     * 添加触觉反馈
     */
    addHapticFeedback() {
        if ('vibrate' in navigator) {
            navigator.vibrate(50); // 轻微震动50ms
        }
    }

    /**
     * 优化触摸目标大小
     */
    optimizeTouchTargets() {
        const touchTargets = document.querySelectorAll('.action-btn, .category-tag, .related-quote-card, .view-more-btn');
        
        touchTargets.forEach(target => {
            const rect = target.getBoundingClientRect();
            
            // 确保触摸目标至少44x44px
            if (rect.width < 44 || rect.height < 44) {
                target.style.minWidth = '44px';
                target.style.minHeight = '44px';
                target.style.display = 'flex';
                target.style.alignItems = 'center';
                target.style.justifyContent = 'center';
            }
        });
    }

    /**
     * 添加移动端特定的CSS类
     */
    addMobileStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .mobile-optimized {
                -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                user-select: none;
            }
            
            .mobile-optimized .action-btn:active,
            .mobile-optimized .category-tag:active,
            .mobile-optimized .related-quote-card:active {
                transform: scale(0.95);
                transition: transform 0.1s ease;
            }
            
            .mobile-optimized .quote-text {
                -webkit-user-select: text;
                user-select: text;
            }
            
            @media (max-width: 640px) {
                .mobile-optimized .quote-detail-card {
                    margin: 0.5rem;
                }
                
                .mobile-optimized .content-grid {
                    padding: 0 0.5rem;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 销毁手势管理器
     */
    destroy() {
        document.removeEventListener('touchstart', this.handleTouchStart);
        document.removeEventListener('touchend', this.handleTouchEnd);
        document.body.classList.remove('mobile-optimized');
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    window.mobileGestureManager = new MobileGestureManager();
    
    // 延迟优化触摸目标，等待页面完全加载
    setTimeout(() => {
        if (window.mobileGestureManager) {
            window.mobileGestureManager.optimizeTouchTargets();
            window.mobileGestureManager.addMobileStyles();
        }
    }, 1000);
});

// 全局注册
window.MobileGestureManager = MobileGestureManager;
