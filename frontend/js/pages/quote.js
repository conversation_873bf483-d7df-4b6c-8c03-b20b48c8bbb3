/**
 * 名言详情页面控制器 - 重构版本
 * 负责名言详情页面的数据加载和交互，实现完整的产品设计方案
 *
 * @version 2.0.0
 * @date 2025-06-28
 * <AUTHOR>
 */

/**
 * 名言详情页控制器类
 */
class QuoteDetailController {
    constructor() {
        this.quoteId = null;
        this.quoteData = null;
        this.isLoading = false;
        this.relatedQuotes = [];
        this.authorInfoWidget = null;
        this.relatedQuotesComponent = null;

        // 初始化组件
        this.initComponents();
    }

    /**
     * 初始化组件
     */
    initComponents() {
        // 延迟初始化，等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupComponents();
            });
        } else {
            this.setupComponents();
        }
    }

    /**
     * 设置组件
     */
    setupComponents() {
        // 初始化相关推荐组件
        const relatedContainer = document.getElementById('related-quotes-container');
        if (relatedContainer && window.RelatedQuotesComponent) {
            this.relatedQuotesComponent = new window.RelatedQuotesComponent(relatedContainer);
        }

        // 初始化作者信息组件
        const authorContainer = document.getElementById('author-info-widget');
        if (authorContainer && window.AuthorInfoWidget) {
            this.authorInfoWidget = new window.AuthorInfoWidget(authorContainer);
        }
    }

    /**
     * 初始化页面
     */
    async init(params = {}) {
        try {
            this.quoteId = params.quoteId || this.parseQuoteIdFromUrl();

            if (!this.quoteId) {
                this.showErrorPage('名言ID无效');
                return;
            }

            console.log('QuoteDetailController: Initializing with quoteId:', this.quoteId);

            // 显示加载状态
            this.showLoadingState();

            // 加载主要内容
            await this.loadQuoteData();

            // 渲染页面内容
            this.renderQuoteContent();

            // 延迟加载相关推荐（性能优化）
            setTimeout(() => {
                this.loadRelatedQuotes();
            }, 500);

            // 初始化交互功能
            this.initInteractions();

            // SEO优化
            this.updateSEOTags();

            // 加载页面组件
            await this.loadPageComponents();

        } catch (error) {
            console.error('Quote page initialization failed:', error);
            this.showErrorPage('页面加载失败');
        }
    }

    /**
     * 加载页面组件
     */
    async loadPageComponents() {
        try {
            // 加载导航栏
            await window.ComponentLoader.loadComponent('navigation', 'navigation-container');

            // 加载热门话题组件
            await window.ComponentLoader.loadComponent('popular-topics', 'popular-topics-container');

            // 渲染作者信息小部件
            this.renderAuthorWidget();

            // 加载页脚
            await window.ComponentLoader.loadComponent('footer', 'footer-container');

        } catch (error) {
            console.error('Error loading page components:', error);
        }
    }

    /**
     * 渲染作者信息小部件
     */
    renderAuthorWidget() {
        if (!this.quoteData || !this.quoteData.author) return;

        console.log('QuoteDetailController: Rendering author widget using new component');

        // 使用新的作者信息组件
        if (this.authorInfoWidget) {
            this.authorInfoWidget.render(this.quoteData.author);
        } else {
            // 降级方案：使用原有逻辑
            this.renderAuthorWidgetLegacy();
        }
    }

    /**
     * 降级方案：原有的作者信息小部件逻辑
     */
    renderAuthorWidgetLegacy() {
        const container = document.getElementById('author-info-widget');
        if (!container) return;

        const author = this.quoteData.author;
        const html = `
            <div class="author-widget">
                <h3 class="widget-title">关于作者</h3>
                <div class="author-summary">
                    <div class="author-avatar-large">
                        ${author.name.charAt(0)}
                    </div>
                    <h4 class="author-name">${author.name}</h4>
                    <p class="author-stats">
                        共有 <strong>${author.quotesCount || 0}</strong> 条名言
                    </p>
                    <a href="${UrlHandler.getAuthorUrl(author)}"
                       class="view-more-btn">
                        查看更多名言 →
                    </a>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    /**
     * 从URL解析名言ID
     */
    parseQuoteIdFromUrl() {
        return UrlHandler.parseQuoteIdFromPath();
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        const quoteContent = document.getElementById('quote-content');
        if (quoteContent) {
            quoteContent.textContent = '正在加载名言内容...';
        }
    }

    /**
     * 加载名言数据
     */
    async loadQuoteData() {
        try {
            console.log('QuoteDetailController: Loading quote data for ID:', this.quoteId);

            this.quoteData = await window.ApiClient.getQuote(this.quoteId);

            if (!this.quoteData) {
                throw new Error('Quote not found');
            }

            console.log('QuoteDetailController: Quote data loaded:', this.quoteData);

        } catch (error) {
            console.error('Failed to load quote data:', error);
            throw error;
        }
    }

    /**
     * 渲染名言内容
     */
    renderQuoteContent() {
        const quote = this.quoteData;

        // 更新名言内容
        this.updateQuoteContent(quote.content);

        // 更新作者信息
        this.renderAuthorInfo(quote.author);

        // 更新分类标签
        this.renderCategories(quote.categories || []);

        // 更新来源信息
        this.renderSourceInfo(quote.sources || []);

        // 更新时间信息
        this.renderDateInfo(quote.createdAt);

        // 生成面包屑
        this.updateBreadcrumb();
    }

    /**
     * 更新名言内容
     */
    updateQuoteContent(content) {
        const quoteContentEl = document.getElementById('quote-content');
        if (quoteContentEl) {
            quoteContentEl.textContent = content;
        }
    }

    /**
     * 渲染作者信息
     */
    renderAuthorInfo(author) {
        const authorInitial = document.getElementById('author-initial');
        const authorLink = document.getElementById('author-link');

        if (authorInitial && author.name) {
            authorInitial.textContent = author.name.charAt(0).toUpperCase();
        }

        if (authorLink && author.name) {
            authorLink.textContent = author.name;
            authorLink.href = UrlHandler.getAuthorUrl(author);
        }
    }

    /**
     * 渲染分类标签
     */
    renderCategories(categories) {
        const container = document.getElementById('categories-container');
        if (!container) return;

        container.innerHTML = '';

        categories.forEach(category => {
            const tag = document.createElement('span');
            tag.className = 'category-tag';
            tag.textContent = category.name;
            tag.addEventListener('click', () => {
                window.location.href = UrlHandler.getCategoryUrl(category);
            });
            container.appendChild(tag);
        });
    }

    /**
     * 渲染来源信息
     */
    renderSourceInfo(sources) {
        const sourceText = document.getElementById('source-text');
        if (!sourceText) return;

        if (sources && sources.length > 0) {
            sourceText.textContent = sources[0].name;
            sourceText.style.display = 'block';
        } else {
            sourceText.style.display = 'none';
        }
    }

    /**
     * 渲染时间信息
     */
    renderDateInfo(createdAt) {
        const quoteDateEl = document.getElementById('quote-date');
        if (!quoteDateEl || !createdAt) return;

        const date = new Date(createdAt);
        quoteDateEl.textContent = date.toLocaleDateString('zh-CN');
    }

    /**
     * 更新面包屑导航
     */
    updateBreadcrumb() {
        if (typeof window.BreadcrumbComponent !== 'undefined') {
            const breadcrumbs = [
                { text: '首页', url: '/', current: false },
                { text: '名言', url: '/quotes/', current: false },
                { text: '名言详情', url: null, current: true }
            ];

            window.BreadcrumbComponent.render('breadcrumb-container', breadcrumbs);
        }
    }
}

    /**
     * 加载相关推荐
     */
    async loadRelatedQuotes() {
        try {
            if (!this.quoteData) return;

            console.log('QuoteDetailController: Loading related quotes using new component');

            // 使用新的相关推荐组件
            if (this.relatedQuotesComponent) {
                await this.relatedQuotesComponent.loadRelatedQuotes(this.quoteId, this.quoteData);
            } else {
                // 降级方案：使用原有逻辑
                await this.loadRelatedQuotesLegacy();
            }

        } catch (error) {
            console.error('Failed to load related quotes:', error);
            // 不阻塞主要功能，只是不显示相关推荐
        }
    }

    /**
     * 降级方案：原有的相关推荐逻辑
     */
    async loadRelatedQuotesLegacy() {
        if (!this.quoteData || !this.quoteData.author) return;

        const authorId = this.quoteData.author.id;
        const categoryIds = (this.quoteData.categories || []).map(c => c.id);

        console.log('QuoteDetailController: Using legacy related quotes logic');

        // 获取同作者名言
        const authorQuotes = await window.ApiClient.getQuotes(1, 3, {
            authorId: authorId,
            excludeId: this.quoteId
        });

        // 获取同分类名言（如果有分类）
        let categoryQuotes = { quotes: [] };
        if (categoryIds.length > 0) {
            categoryQuotes = await window.ApiClient.getQuotes(1, 2, {
                categoryId: categoryIds[0],
                excludeId: this.quoteId
            });
        }

        this.relatedQuotes = [
            ...(authorQuotes.quotes || []),
            ...(categoryQuotes.quotes || [])
        ];

        console.log('QuoteDetailController: Related quotes loaded (legacy):', this.relatedQuotes.length);

        this.renderRelatedQuotes();
    }

    /**
     * 渲染相关推荐
     */
    renderRelatedQuotes() {
        const container = document.getElementById('related-quotes-container');
        if (!container) return;

        // 隐藏加载状态
        const loadingEl = document.getElementById('related-quotes-loading');
        if (loadingEl) {
            loadingEl.style.display = 'none';
        }

        if (this.relatedQuotes.length === 0) {
            container.innerHTML = '<p class="text-gray-500 dark:text-gray-400 text-center py-8">暂无相关推荐</p>';
            return;
        }

        const html = `
            <div class="related-quotes-section">
                <h2 class="section-title">
                    <i class="fas fa-quote-right text-yellow-500 mr-2"></i>
                    相关名言推荐
                </h2>
                <div class="related-quotes-list">
                    ${this.relatedQuotes.map(quote => this.renderQuoteCard(quote)).join('')}
                </div>
            </div>
        `;

        container.innerHTML = html;
        this.bindRelatedQuotesEvents();
    }

    /**
     * 渲染单个名言卡片
     */
    renderQuoteCard(quote) {
        return `
            <article class="related-quote-card" data-quote-id="${quote.id}">
                <blockquote class="quote-content">
                    "${quote.content}"
                </blockquote>
                <cite class="quote-author">
                    — ${quote.author ? quote.author.name : '未知作者'}
                </cite>
            </article>
        `;
    }

    /**
     * 绑定相关推荐事件
     */
    bindRelatedQuotesEvents() {
        const cards = document.querySelectorAll('.related-quote-card');
        cards.forEach(card => {
            card.addEventListener('click', (e) => {
                const quoteId = e.currentTarget.dataset.quoteId;
                window.location.href = `/quotes/${quoteId}/`;
            });
        });
    }

    /**
     * 初始化交互功能
     */
    initInteractions() {
        // 分享功能
        const shareBtn = document.getElementById('share-button');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.shareQuote());
        }

        // 复制功能
        const copyBtn = document.getElementById('copy-button');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => this.copyQuote());
        }

        // 收藏功能
        const favoriteBtn = document.getElementById('favorite-button');
        if (favoriteBtn) {
            favoriteBtn.addEventListener('click', () => this.toggleFavorite());
        }
    }

    /**
     * 分享名言
     */
    shareQuote() {
        if (!this.quoteData) return;

        const quote = this.quoteData;
        const shareText = `"${quote.content}" - ${quote.author ? quote.author.name : '未知作者'}`;
        const shareUrl = window.location.href;

        if (navigator.share) {
            navigator.share({
                title: '分享名言',
                text: shareText,
                url: shareUrl
            }).catch(err => {
                console.log('Share failed:', err);
                this.fallbackShare(shareText, shareUrl);
            });
        } else {
            this.fallbackShare(shareText, shareUrl);
        }
    }

    /**
     * 降级分享方案
     */
    fallbackShare(text, url) {
        this.copyToClipboard(text + '\n' + url);
        this.showToast('链接已复制到剪贴板');
    }

    /**
     * 复制名言
     */
    copyQuote() {
        if (!this.quoteData) return;

        const quote = this.quoteData;
        const copyText = `"${quote.content}" - ${quote.author ? quote.author.name : '未知作者'}`;

        this.copyToClipboard(copyText);
        this.showToast('名言已复制到剪贴板');
    }

    /**
     * 复制到剪贴板
     */
    copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).catch(err => {
                console.error('Failed to copy:', err);
                this.fallbackCopy(text);
            });
        } else {
            this.fallbackCopy(text);
        }
    }

    /**
     * 降级复制方案
     */
    fallbackCopy(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
        } catch (err) {
            console.error('Fallback copy failed:', err);
        }
        document.body.removeChild(textArea);
    }

    /**
     * 切换收藏状态
     */
    toggleFavorite() {
        // 未来功能：实现收藏功能
        this.showToast('收藏功能即将上线');
    }

    /**
     * 显示提示消息
     */
    showToast(message) {
        // 简单的提示实现
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-yellow-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

/**
 * 加载页面组件
 */
async function loadPageComponents() {
    // 加载面包屑组件
    await ComponentLoader.loadComponent('breadcrumb-container', 'breadcrumb');

    // 初始化面包屑导航
    BreadcrumbComponent.init('breadcrumb-list');

    // 加载热门主题组件
    await ComponentLoader.loadComponent('popular-topics-container', 'popular-topics');
}

/**
 * 加载页面数据
 */
async function loadPageData() {
    // 显示加载状态
    showLoadingState();

    try {
        // 获取名言详情
        console.log('Getting quote by ID:', quotePageState.quoteId);
        const quote = await window.ApiClient.getQuote(quotePageState.quoteId);
        console.log('Quote result:', quote);
        if (!quote) {
            showErrorMessage(`Quote with ID ${quotePageState.quoteId} not found.`);
            hideLoadingState();
            return;
        }

        // 更新页面标题和描述
        updatePageMetadata(quote);

        // 渲染名言详情
        renderQuoteDetails(quote);

        // 保存作者ID
        quotePageState.authorId = quote.author.id;

        // 并行加载其他数据
        const [categoriesData, authorsData, sourcesData, relatedQuotesData] = await Promise.all([
            loadCategories(),
            loadAuthors(),
            loadSources(),
            loadRelatedQuotes()
        ]);

        // 隐藏加载状态
        hideLoadingState();

    } catch (error) {
        console.error('Error loading page data:', error);
        showErrorMessage('Failed to load data. Please try again later.');
        hideLoadingState();
    }
}

/**
 * 更新页面元数据
 * @param {Object} quote - 名言对象
 */
function updatePageMetadata(quote) {
    // 截断长名言
    const shortQuote = quote.content.length > 100
        ? quote.content.substring(0, 100) + '...'
        : quote.content;

    document.title = `"${shortQuote}" - ${quote.author.name} | Quotes Collection`;

    // 更新meta描述
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        metaDescription.content = `"${shortQuote}" - ${quote.author.name}. Explore this inspiring quote and more wisdom from ${quote.author.name}.`;
    }

    // 更新canonical链接
    const canonicalLink = document.querySelector('link[rel="canonical"]');
    if (canonicalLink) {
        canonicalLink.href = `https://quotese.com/quotes/${UrlHandler.slugify(quote.content.substring(0, 50))}-${quote.id}.html`;
    }

    // 添加Quote结构化数据
    addQuoteStructuredData(quote);

    // 更新社交媒体元数据
    if (window.SocialMetaUtil) {
        window.SocialMetaUtil.updateQuoteMetaTags(quote);
    }
}

/**
 * 渲染名言详情
 * @param {Object} quote - 名言对象
 */
function renderQuoteDetails(quote) {
    // 更新名言内容
    const contentElement = document.getElementById('quote-content');
    if (contentElement) {
        contentElement.textContent = `"${quote.content}"`;
    }

    // 更新作者信息
    const authorLinkElement = document.getElementById('author-link');
    if (authorLinkElement) {
        authorLinkElement.href = UrlHandler.getAuthorUrl(quote.author);
        authorLinkElement.textContent = quote.author.name;
    }

    // 更新作者首字母
    const authorInitialElement = document.getElementById('author-initial');
    if (authorInitialElement) {
        authorInitialElement.textContent = quote.author.name.charAt(0);
    }

    // 更新来源信息
    const sourceTextElement = document.getElementById('source-text');
    if (sourceTextElement) {
        sourceTextElement.innerHTML = quote.sources.map(source =>
            `<a href="${UrlHandler.getSourceUrl(source)}" class="hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors duration-300">${source.name}</a>`
        ).join(', ');
    }

    // 更新类别标签
    const categoriesContainer = document.getElementById('categories-container');
    if (categoriesContainer) {
        categoriesContainer.innerHTML = '';

        quote.categories.forEach(category => {
            const categoryTag = document.createElement('a');
            categoryTag.href = UrlHandler.getCategoryUrl(category);
            categoryTag.className = 'tag px-3 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors duration-300';
            categoryTag.textContent = category.name;

            categoriesContainer.appendChild(categoryTag);
        });
    }

    // 更新日期信息
    const dateElement = document.getElementById('quote-date');
    if (dateElement) {
        const date = new Date(quote.updatedAt || quote.createdAt);
        dateElement.textContent = `Updated: ${date.toLocaleDateString()}`;
    }

    // 更新相关名言标题
    const authorHeadingElement = document.getElementById('author-name-heading');
    if (authorHeadingElement) {
        authorHeadingElement.textContent = quote.author.name;
    }
}

/**
 * 加载类别列表
 * @returns {Promise<Object>} - 类别列表
 */
async function loadCategories() {
    try {
        // 获取类别列表
        const categoriesData = await window.ApiClient.getCategories(20);

        // 渲染类别列表
        renderCategories(categoriesData.categories);

        return categoriesData;
    } catch (error) {
        console.error('Error loading categories:', error);
        throw error;
    }
}

/**
 * 加载作者列表
 * @returns {Promise<Object>} - 作者列表
 */
async function loadAuthors() {
    try {
        // 获取作者列表
        const authorsData = await window.ApiClient.getAuthors(5);

        // 渲染作者列表
        renderAuthors(authorsData.authors);

        return authorsData;
    } catch (error) {
        console.error('Error loading authors:', error);
        throw error;
    }
}

/**
 * 加载来源列表
 * @returns {Promise<Object>} - 来源列表
 */
async function loadSources() {
    try {
        // 获取来源列表
        const sourcesData = await window.ApiClient.getSources(5);

        // 渲染来源列表
        renderSources(sourcesData.sources);

        return sourcesData;
    } catch (error) {
        console.error('Error loading sources:', error);
        throw error;
    }
}

/**
 * 加载相关名言
 * @returns {Promise<Object>} - 相关名言列表
 */
async function loadRelatedQuotes() {
    try {
        // 获取相关名言列表（同一作者的其他名言）
        const filters = { authorId: quotePageState.authorId };
        const quotesData = await window.ApiClient.getQuotes(1, 5, filters);

        // 过滤掉当前名言
        const relatedQuotes = quotesData.quotes.filter(quote => quote.id !== parseInt(quotePageState.quoteId));

        // 渲染相关名言列表
        renderRelatedQuotes(relatedQuotes);

        return quotesData;
    } catch (error) {
        console.error('Error loading related quotes:', error);
        throw error;
    }
}

/**
 * 渲染相关名言列表
 * @param {Array} quotes - 名言列表
 */
function renderRelatedQuotes(quotes) {
    const relatedQuotesContainer = document.getElementById('related-quotes-container');
    if (!relatedQuotesContainer) return;

    // 清空容器
    relatedQuotesContainer.innerHTML = '';

    if (quotes.length === 0) {
        // 显示空状态
        relatedQuotesContainer.innerHTML = `
            <div class="text-center py-8">
                <p class="text-gray-500 dark:text-gray-400">No other quotes found by this author.</p>
            </div>
        `;
        return;
    }

    // 创建名言卡片
    quotes.forEach((quote, index) => {
        const quoteCard = document.createElement('div');
        quoteCard.className = `quote-card-component relative p-4 sm:p-5 md:p-6 quote-marks fade-in fade-in-delay-${index % 3} mb-4 sm:mb-6 card-hover-effect cursor-pointer`;

        // 截断长名言
        const displayContent = quote.content.length > 150
            ? quote.content.substring(0, 150) + '...'
            : quote.content;

        // 创建名言卡片内容
        quoteCard.innerHTML = `
            <div class="relative z-10">
                <p class="text-base sm:text-lg font-serif leading-relaxed mb-3 sm:mb-4">"${displayContent}"</p>
                <div class="flex items-center mt-3 sm:mt-4">
                    <div class="flex-shrink-0 mr-4">
                        <div class="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-600 dark:text-yellow-300 border-2 border-yellow-400 dark:border-yellow-600">
                            <span class="text-sm font-bold">${quote.author.name.charAt(0)}</span>
                        </div>
                    </div>
                    <div>
                        <a href="${UrlHandler.getAuthorUrl(quote.author)}" class="font-semibold text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300 transition-colors duration-300">${quote.author.name}</a>
                        <p class="text-sm text-gray-500 dark:text-gray-400">${quote.sources.map(source => source.name).join(', ')}</p>
                    </div>
                </div>
            </div>
            <div class="mt-3 sm:mt-4 flex flex-wrap gap-1 sm:gap-2">
                ${quote.categories.map(category =>
                    `<a href="${UrlHandler.getCategoryUrl(category)}" class="tag px-2 py-0.5 sm:px-3 sm:py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors duration-300">${category.name}</a>`
                ).join('')}
            </div>
            <div class="absolute bottom-2 sm:bottom-3 right-2 sm:right-4 flex items-center space-x-1">
                <button class="p-2 text-gray-500 hover:text-yellow-600 dark:text-gray-400 dark:hover:text-yellow-400 transition-colors duration-300 rounded-full hover:bg-yellow-50 dark:hover:bg-gray-700" title="Share" data-quote-id="${quote.id}">
                    <i class="fas fa-share-alt"></i>
                </button>
                <button class="p-2 text-gray-500 hover:text-yellow-600 dark:text-gray-400 dark:hover:text-yellow-400 transition-colors duration-300 rounded-full hover:bg-yellow-50 dark:hover:bg-gray-700" title="View Details" data-quote-id="${quote.id}">
                    <i class="fas fa-external-link-alt"></i>
                </button>
            </div>
        `;

        // 添加到容器
        relatedQuotesContainer.appendChild(quoteCard);

        // 添加点击事件
        quoteCard.addEventListener('click', (e) => {
            // 只有在没有点击按钮或链接时才触发
            if (!e.target.closest('button') && !e.target.closest('a')) {
                window.location.href = UrlHandler.getQuoteUrl({
                    id: quote.id,
                    content: quote.content
                });
            }
        });
    });
}

/**
 * 渲染类别列表
 * @param {Array} categories - 类别列表
 */
function renderCategories(categories) {
    const categoriesContainer = document.getElementById('categories-container');
    if (!categoriesContainer) return;

    // 清空容器
    categoriesContainer.innerHTML = '';

    if (categories.length === 0) {
        categoriesContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400">No categories found.</p>';
        return;
    }

    // 按照引用数量排序
    const sortedCategories = [...categories].sort((a, b) => b.count - a.count);

    // 创建类别标签
    sortedCategories.forEach(category => {
        const categoryTag = document.createElement('a');
        categoryTag.href = UrlHandler.getCategoryUrl(category);
        categoryTag.className = 'tag px-3 py-1 text-sm rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors duration-300';
        categoryTag.textContent = `${category.name} (${category.count})`;

        categoriesContainer.appendChild(categoryTag);
    });
}

/**
 * 渲染作者列表
 * @param {Array} authors - 作者列表
 */
function renderAuthors(authors) {
    const authorsContainer = document.getElementById('authors-container');
    if (!authorsContainer) return;

    // 清空容器
    authorsContainer.innerHTML = '';

    if (authors.length === 0) {
        authorsContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400">No authors found.</p>';
        return;
    }

    // 按照引用数量排序
    const sortedAuthors = [...authors].sort((a, b) => b.count - a.count);

    // 创建作者列表项
    sortedAuthors.forEach(author => {
        const maxCount = sortedAuthors[0].count;
        const percentage = Math.round((author.count / maxCount) * 100);

        const authorItem = document.createElement('li');

        // 当前作者高亮显示
        if (author.id === quotePageState.authorId) {
            authorItem.className = 'flex justify-between items-center p-2 rounded-md bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800';
        } else {
            authorItem.className = 'flex justify-between items-center p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-300';
        }

        authorItem.innerHTML = `
            <div class="flex items-center space-x-2 w-full">
                <div class="w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-600 dark:text-yellow-300 flex-shrink-0 border-2 border-yellow-400 dark:border-yellow-600">
                    <span class="text-xs font-bold">${author.name.charAt(0)}</span>
                </div>
                <div class="flex-grow ml-2">
                    <a href="${UrlHandler.getAuthorUrl(author)}" class="${author.id === quotePageState.authorId ? 'text-yellow-600 dark:text-yellow-400 font-semibold' : 'hover:text-yellow-600 dark:hover:text-yellow-400'} font-medium transition-colors duration-300">${author.name}</a>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1">
                        <div class="bg-yellow-500 dark:bg-yellow-400 h-1.5 rounded-full" style="width: ${percentage}%"></div>
                    </div>
                </div>
                <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">${author.count}</span>
            </div>
        `;

        authorsContainer.appendChild(authorItem);
    });
}

/**
 * 渲染来源列表
 * @param {Array} sources - 来源列表
 */
function renderSources(sources) {
    const sourcesContainer = document.getElementById('sources-container');
    if (!sourcesContainer) return;

    // 清空容器
    sourcesContainer.innerHTML = '';

    if (sources.length === 0) {
        sourcesContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400">No sources found.</p>';
        return;
    }

    // 按照引用数量排序
    const sortedSources = [...sources].sort((a, b) => b.count - a.count);

    // 创建来源列表项
    sortedSources.forEach(source => {
        const sourceItem = document.createElement('li');
        sourceItem.className = 'flex justify-between items-center p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-300';

        // 根据来源类型选择不同的图标
        let iconClass = 'fas fa-book';

        if (source.name.toLowerCase().includes('speech') || source.name.toLowerCase().includes('address')) {
            iconClass = 'fas fa-microphone';
        } else if (source.name.toLowerCase().includes('interview')) {
            iconClass = 'fas fa-comments';
        } else if (source.name.toLowerCase().includes('letter') || source.name.toLowerCase().includes('correspondence')) {
            iconClass = 'fas fa-envelope';
        } else if (source.name.toLowerCase().includes('article') || source.name.toLowerCase().includes('magazine')) {
            iconClass = 'fas fa-newspaper';
        } else if (source.name.toLowerCase().includes('movie') || source.name.toLowerCase().includes('film')) {
            iconClass = 'fas fa-film';
        } else if (source.name.toLowerCase().includes('song') || source.name.toLowerCase().includes('music')) {
            iconClass = 'fas fa-music';
        }

        sourceItem.innerHTML = `
            <div class="flex items-center space-x-2 w-full">
                <div class="w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-600 dark:text-yellow-300 flex-shrink-0">
                    <i class="${iconClass} text-xs"></i>
                </div>
                <div class="flex-grow">
                    <a href="${UrlHandler.getSourceUrl(source)}" class="hover:text-yellow-600 dark:hover:text-yellow-400 font-medium transition-colors duration-300">${source.name}</a>
                </div>
                <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">${source.count}</span>
            </div>
        `;

        sourcesContainer.appendChild(sourceItem);
    });
}

/**
 * 显示加载状态
 */
function showLoadingState() {
    quotePageState.isLoading = true;

    // 显示名言详情加载状态
    const quoteCardContainer = document.getElementById('quote-card-container');
    if (quoteCardContainer) {
        quoteCardContainer.innerHTML = `
            <div class="flex justify-center py-12">
                <div class="loading-spinner" role="status">
                    <span class="sr-only">Loading quote...</span>
                </div>
            </div>
        `;
    }

    // 显示相关名言加载状态
    const relatedQuotesContainer = document.getElementById('related-quotes-container');
    if (relatedQuotesContainer) {
        relatedQuotesContainer.innerHTML = `
            <div class="flex justify-center py-12">
                <div class="loading-spinner" role="status">
                    <span class="sr-only">Loading related quotes...</span>
                </div>
            </div>
        `;
    }
}

/**
 * 隐藏加载状态
 */
function hideLoadingState() {
    quotePageState.isLoading = false;
}

/**
 * 显示错误消息
 * @param {string} message - 错误消息
 */
function showErrorMessage(message) {
    const quoteCardContainer = document.getElementById('quote-card-container');
    if (quoteCardContainer) {
        quoteCardContainer.innerHTML = `
            <div class="bg-red-100 text-red-800 p-4 rounded-md">
                <i class="fas fa-exclamation-circle mr-2"></i>
                ${message}
            </div>
        `;
    }
}

/**
 * 初始化事件监听器
 */
function initEventListeners() {
    // 分享按钮
    const shareButton = document.getElementById('share-button');
    if (shareButton) {
        shareButton.addEventListener('click', shareQuote);
    }

    // 复制按钮
    const copyButton = document.getElementById('copy-button');
    if (copyButton) {
        copyButton.addEventListener('click', copyQuoteToClipboard);
    }

    // 初始化增强的交互功能
    if (window.quoteDetailController && window.quoteDetailController.initEnhancedInteractions) {
        window.quoteDetailController.initEnhancedInteractions();
    }
}

/**
 * 分享名言
 */
function shareQuote() {
    // 如果支持Web Share API
    if (navigator.share) {
        const quoteContent = document.getElementById('quote-content').textContent;
        const authorName = document.getElementById('author-link').textContent;

        navigator.share({
            title: `Quote by ${authorName}`,
            text: `${quoteContent} - ${authorName}`,
            url: window.location.href
        })
        .then(() => console.log('Quote shared successfully'))
        .catch((error) => console.error('Error sharing quote:', error));
    } else {
        // 如果不支持Web Share API，复制链接到剪贴板
        copyToClipboard(window.location.href);

        // 显示提示
        alert('Link copied to clipboard!');
    }
}

/**
 * 复制名言到剪贴板
 */
function copyQuoteToClipboard() {
    const quoteContent = document.getElementById('quote-content').textContent;
    const authorName = document.getElementById('author-link').textContent;

    const textToCopy = `${quoteContent} - ${authorName}`;

    copyToClipboard(textToCopy);

    // 显示提示
    alert('Quote copied to clipboard!');
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 */
function copyToClipboard(text) {
    // 创建临时元素
    const tempElement = document.createElement('textarea');
    tempElement.value = text;
    tempElement.setAttribute('readonly', '');
    tempElement.style.position = 'absolute';
    tempElement.style.left = '-9999px';

    document.body.appendChild(tempElement);

    // 选择文本并复制
    tempElement.select();
    document.execCommand('copy');

    // 移除临时元素
    document.body.removeChild(tempElement);
}

/**
 * 添加Quote结构化数据
 * @param {Object} quote - 名言对象
 */
function addQuoteStructuredData(quote) {
    // 移除旧的结构化数据
    const oldScript = document.getElementById('quote-structured-data');
    if (oldScript) {
        oldScript.remove();
    }

    // 创建结构化数据
    const structuredData = {
        "@context": "https://schema.org",
        "@type": "Quotation",
        "text": quote.content,
        "author": {
            "@type": "Person",
            "name": quote.author.name,
            "url": `https://quotese.com${UrlHandler.getAuthorUrl(quote.author)}`
        },
        "dateCreated": quote.createdAt || new Date().toISOString(),
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": `https://quotese.com${UrlHandler.getQuoteUrl(quote)}`
        }
    };

    // 添加来源信息（如果有）
    if (quote.sources && quote.sources.length > 0) {
        structuredData.isPartOf = {
            "@type": "CreativeWork",
            "name": quote.sources[0].name,
            "url": `https://quotese.com${UrlHandler.getSourceUrl(quote.sources[0])}`
        };
    }

    // 添加类别信息（如果有）
    if (quote.categories && quote.categories.length > 0) {
        structuredData.keywords = quote.categories.map(category => category.name).join(',');
    }

    // 添加到页面
    const script = document.createElement('script');
    script.id = 'quote-structured-data';
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);
}

// ==================== 新版控制器类扩展方法 ====================

/**
 * 扩展现有功能，添加新的交互和SEO优化
 */

// 创建全局控制器实例（基于现有功能扩展）
if (!window.quoteDetailController) {
    window.quoteDetailController = {
        // 分享名言
        shareQuote: function() {
            const quoteContent = document.getElementById('quote-content')?.textContent || '';
            const authorName = document.getElementById('author-link')?.textContent || '未知作者';
            const shareText = `"${quoteContent}" - ${authorName}`;
            const shareUrl = window.location.href;

            if (navigator.share) {
                navigator.share({
                    title: '分享名言',
                    text: shareText,
                    url: shareUrl
                }).catch(err => {
                    console.log('Share failed:', err);
                    this.fallbackShare(shareText, shareUrl);
                });
            } else {
                this.fallbackShare(shareText, shareUrl);
            }
        },

        // 降级分享方案
        fallbackShare: function(text, url) {
            this.copyToClipboard(text + '\n' + url);
            this.showToast('链接已复制到剪贴板');
        },

        // 复制名言
        copyQuote: function() {
            const quoteContent = document.getElementById('quote-content')?.textContent || '';
            const authorName = document.getElementById('author-link')?.textContent || '未知作者';
            const copyText = `"${quoteContent}" - ${authorName}`;

            this.copyToClipboard(copyText);
            this.showToast('名言已复制到剪贴板');
        },

        // 复制到剪贴板
        copyToClipboard: function(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).catch(err => {
                    console.error('Failed to copy:', err);
                    this.fallbackCopy(text);
                });
            } else {
                this.fallbackCopy(text);
            }
        },

        // 降级复制方案
        fallbackCopy: function(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
            } catch (err) {
                console.error('Fallback copy failed:', err);
            }
            document.body.removeChild(textArea);
        },

        // 切换收藏状态
        toggleFavorite: function() {
            const favoriteBtn = document.getElementById('favorite-button');
            if (favoriteBtn) {
                const icon = favoriteBtn.querySelector('i');
                if (icon.classList.contains('far')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    favoriteBtn.classList.add('active');
                    this.showToast('已添加到收藏');
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    favoriteBtn.classList.remove('active');
                    this.showToast('已取消收藏');
                }
            }
        },

        // 显示提示消息
        showToast: function(message) {
            // 移除现有的toast
            const existingToast = document.querySelector('.quote-toast');
            if (existingToast) {
                existingToast.remove();
            }

            // 创建新的toast
            const toast = document.createElement('div');
            toast.className = 'quote-toast fixed top-4 right-4 bg-yellow-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transform transition-all duration-300';
            toast.textContent = message;
            document.body.appendChild(toast);

            // 动画显示
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 3000);
        },

        // 性能优化：预加载关键资源
        preloadCriticalResources: function() {
            // 预加载作者页面（用户可能会点击）
            const authorLink = document.getElementById('author-link');
            if (authorLink && authorLink.href) {
                this.preloadPage(authorLink.href);
            }

            // 预加载分类页面
            const categoryTags = document.querySelectorAll('.category-tag');
            categoryTags.forEach((tag, index) => {
                if (index < 2) { // 只预加载前两个分类
                    tag.addEventListener('mouseenter', () => {
                        // 在鼠标悬停时预加载
                        const categoryUrl = tag.getAttribute('data-category-url');
                        if (categoryUrl) {
                            this.preloadPage(categoryUrl);
                        }
                    }, { once: true });
                }
            });
        },

        // 预加载页面
        preloadPage: function(url) {
            if (!url) return;

            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = url;
            document.head.appendChild(link);

            console.log('QuoteDetailController: Preloading page:', url);
        },

        // 性能监控
        trackPerformance: function() {
            if (window.performance && window.performance.timing) {
                const timing = window.performance.timing;
                const loadTime = timing.loadEventEnd - timing.navigationStart;

                console.log('QuoteDetailController: Page load time:', loadTime + 'ms');

                // 如果加载时间超过3秒，记录性能问题
                if (loadTime > 3000) {
                    console.warn('QuoteDetailController: Slow page load detected:', loadTime + 'ms');
                }
            }
        },

        // 初始化新的交互功能
        initEnhancedInteractions: function() {
            // 分享功能
            const shareBtn = document.getElementById('share-button');
            if (shareBtn) {
                shareBtn.addEventListener('click', () => this.shareQuote());
            }

            // 复制功能
            const copyBtn = document.getElementById('copy-button');
            if (copyBtn) {
                copyBtn.addEventListener('click', () => this.copyQuote());
            }

            // 收藏功能
            const favoriteBtn = document.getElementById('favorite-button');
            if (favoriteBtn) {
                favoriteBtn.addEventListener('click', () => this.toggleFavorite());
            }
        }
    };
}

// 页面初始化现在由PageRouter负责
// 保留此函数供PageRouter调用
