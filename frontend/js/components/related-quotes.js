/**
 * 相关名言推荐组件
 * 负责显示和管理相关名言推荐功能
 *
 * @version 1.0.0
 * @date 2025-06-28
 * <AUTHOR>
 */

/**
 * 相关名言推荐组件类
 */
class RelatedQuotesComponent {
    constructor(container) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        this.quotes = [];
        this.currentQuoteId = null;
        this.isLoading = false;
        this.observer = null;

        // 初始化Intersection Observer
        this.initIntersectionObserver();
    }

    /**
     * 初始化Intersection Observer用于懒加载
     */
    initIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // 当容器进入视口时，开始加载相关推荐
                        this.observer.unobserve(entry.target);
                        console.log('RelatedQuotesComponent: Container in viewport, loading quotes');
                    }
                });
            }, {
                rootMargin: '100px' // 提前100px开始加载
            });
        }
    }

    /**
     * 加载相关推荐
     * @param {number} quoteId - 当前名言ID
     * @param {Object} quoteData - 当前名言数据
     */
    async loadRelatedQuotes(quoteId, quoteData) {
        if (!quoteData || this.isLoading) return;
        
        this.currentQuoteId = quoteId;
        this.isLoading = true;
        this.showLoadingState();
        
        try {
            const relatedQuotes = await this.fetchRelatedQuotes(quoteData);
            this.quotes = relatedQuotes;
            this.render();
        } catch (error) {
            console.error('Failed to load related quotes:', error);
            this.showErrorState();
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * 获取相关推荐数据
     * @param {Object} quoteData - 当前名言数据
     * @returns {Array} 相关名言列表
     */
    async fetchRelatedQuotes(quoteData) {
        const relatedQuotes = [];
        
        try {
            // 1. 获取同作者名言（优先级最高）
            if (quoteData.author && quoteData.author.id) {
                const authorQuotes = await this.getAuthorQuotes(quoteData.author.id);
                relatedQuotes.push(...authorQuotes.slice(0, 3));
            }
            
            // 2. 获取同分类名言（次优先级）
            if (quoteData.categories && quoteData.categories.length > 0) {
                const categoryQuotes = await this.getCategoryQuotes(quoteData.categories[0].id);
                relatedQuotes.push(...categoryQuotes.slice(0, 2));
            }
            
            // 3. 如果数量不足，获取热门名言补充
            if (relatedQuotes.length < 3) {
                const popularQuotes = await this.getPopularQuotes();
                relatedQuotes.push(...popularQuotes.slice(0, 3 - relatedQuotes.length));
            }
            
            // 去重并限制数量
            const uniqueQuotes = this.removeDuplicates(relatedQuotes);
            return uniqueQuotes.slice(0, 5);
            
        } catch (error) {
            console.error('Error fetching related quotes:', error);
            return [];
        }
    }

    /**
     * 获取同作者名言
     * @param {number} authorId - 作者ID
     * @returns {Array} 同作者名言列表
     */
    async getAuthorQuotes(authorId) {
        try {
            const result = await window.ApiClient.getQuotes(1, 5, {
                authorId: authorId,
                excludeId: this.currentQuoteId
            });
            return result.quotes || [];
        } catch (error) {
            console.error('Error fetching author quotes:', error);
            return [];
        }
    }

    /**
     * 获取同分类名言
     * @param {number} categoryId - 分类ID
     * @returns {Array} 同分类名言列表
     */
    async getCategoryQuotes(categoryId) {
        try {
            const result = await window.ApiClient.getQuotes(1, 3, {
                categoryId: categoryId,
                excludeId: this.currentQuoteId
            });
            return result.quotes || [];
        } catch (error) {
            console.error('Error fetching category quotes:', error);
            return [];
        }
    }

    /**
     * 获取热门名言
     * @returns {Array} 热门名言列表
     */
    async getPopularQuotes() {
        try {
            const result = await window.ApiClient.getQuotes(1, 5, {
                orderBy: 'popular',
                excludeId: this.currentQuoteId
            });
            return result.quotes || [];
        } catch (error) {
            console.error('Error fetching popular quotes:', error);
            return [];
        }
    }

    /**
     * 去除重复的名言
     * @param {Array} quotes - 名言列表
     * @returns {Array} 去重后的名言列表
     */
    removeDuplicates(quotes) {
        const seen = new Set([this.currentQuoteId]);
        return quotes.filter(quote => {
            if (seen.has(quote.id)) {
                return false;
            }
            seen.add(quote.id);
            return true;
        });
    }

    /**
     * 渲染组件
     */
    render() {
        if (!this.container) return;
        
        if (this.quotes.length === 0) {
            this.showEmptyState();
            return;
        }
        
        const html = `
            <div class="related-quotes-section">
                <h2 class="section-title">
                    <i class="fas fa-quote-right text-yellow-500 mr-2"></i>
                    相关名言推荐
                </h2>
                <div class="related-quotes-list">
                    ${this.quotes.map(quote => this.renderQuoteCard(quote)).join('')}
                </div>
            </div>
        `;
        
        this.container.innerHTML = html;
        this.bindEvents();
    }

    /**
     * 渲染单个名言卡片
     * @param {Object} quote - 名言对象
     * @returns {string} HTML字符串
     */
    renderQuoteCard(quote) {
        const authorName = quote.author ? quote.author.name : '未知作者';
        const truncatedContent = this.truncateText(quote.content, 120);
        
        return `
            <article class="related-quote-card" data-quote-id="${quote.id}">
                <blockquote class="quote-content">
                    "${truncatedContent}"
                </blockquote>
                <cite class="quote-author">
                    — ${authorName}
                </cite>
                <div class="quote-card-overlay">
                    <i class="fas fa-external-link-alt"></i>
                </div>
            </article>
        `;
    }

    /**
     * 截断文本
     * @param {string} text - 原始文本
     * @param {number} maxLength - 最大长度
     * @returns {string} 截断后的文本
     */
    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        const cards = this.container.querySelectorAll('.related-quote-card');
        cards.forEach(card => {
            card.addEventListener('click', (e) => {
                const quoteId = e.currentTarget.dataset.quoteId;
                this.navigateToQuote(quoteId);
            });
            
            // 添加hover效果
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    }

    /**
     * 导航到名言详情页
     * @param {number} quoteId - 名言ID
     */
    navigateToQuote(quoteId) {
        const url = `/quotes/${quoteId}/`;
        console.log('RelatedQuotesComponent: Navigating to quote:', url);
        window.location.href = url;
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="related-quotes-section">
                <h2 class="section-title">
                    <i class="fas fa-quote-right text-yellow-500 mr-2"></i>
                    相关名言推荐
                </h2>
                <div class="flex justify-center py-12">
                    <div class="loading-spinner" role="status">
                        <span class="sr-only">Loading related quotes...</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="related-quotes-section">
                <h2 class="section-title">
                    <i class="fas fa-quote-right text-yellow-500 mr-2"></i>
                    相关名言推荐
                </h2>
                <div class="text-center py-12">
                    <div class="mb-4">
                        <i class="fas fa-quote-left text-4xl text-gray-300 dark:text-gray-600"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400">
                        暂无相关推荐
                    </p>
                </div>
            </div>
        `;
    }

    /**
     * 显示错误状态
     */
    showErrorState() {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="related-quotes-section">
                <h2 class="section-title">
                    <i class="fas fa-quote-right text-yellow-500 mr-2"></i>
                    相关名言推荐
                </h2>
                <div class="text-center py-12">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-4xl text-red-400"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">
                        加载推荐内容时出现错误
                    </p>
                    <button onclick="location.reload()" class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors duration-300">
                        重试
                    </button>
                </div>
            </div>
        `;
    }
}

// 全局注册组件
window.RelatedQuotesComponent = RelatedQuotesComponent;
