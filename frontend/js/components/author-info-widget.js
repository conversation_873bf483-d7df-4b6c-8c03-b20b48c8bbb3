/**
 * 作者信息扩展组件
 * 在名言详情页侧边栏显示作者相关信息
 *
 * @version 1.0.0
 * @date 2025-06-28
 * <AUTHOR>
 */

/**
 * 作者信息小部件类
 */
class AuthorInfoWidget {
    constructor(container) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        this.authorData = null;
    }

    /**
     * 渲染作者信息
     * @param {Object} author - 作者对象
     */
    render(author) {
        if (!this.container || !author) return;
        
        this.authorData = author;
        
        const html = `
            <div class="author-widget">
                <h3 class="widget-title">关于作者</h3>
                <div class="author-summary">
                    <div class="author-avatar-large">
                        ${this.getAuthorInitial(author.name)}
                    </div>
                    <h4 class="author-name">${author.name}</h4>
                    <p class="author-stats">
                        共有 <strong>${author.quotesCount || 0}</strong> 条名言
                    </p>
                    ${this.renderAuthorDescription(author)}
                    <div class="author-actions">
                        <a href="${this.getAuthorUrl(author)}" 
                           class="view-more-btn">
                            <i class="fas fa-quote-right mr-2"></i>
                            查看更多名言
                        </a>
                    </div>
                </div>
            </div>
        `;
        
        this.container.innerHTML = html;
        this.bindEvents();
    }

    /**
     * 获取作者姓名首字母
     * @param {string} name - 作者姓名
     * @returns {string} 首字母
     */
    getAuthorInitial(name) {
        if (!name) return 'A';
        return name.charAt(0).toUpperCase();
    }

    /**
     * 渲染作者描述（如果有的话）
     * @param {Object} author - 作者对象
     * @returns {string} HTML字符串
     */
    renderAuthorDescription(author) {
        // 这里可以根据作者信息显示简短描述
        // 目前先显示基本信息，未来可以从API获取更详细的作者信息
        const descriptions = {
            'Theodore Roosevelt': '美国第26任总统，政治家、作家、探险家',
            'Albert Einstein': '理论物理学家，相对论创立者',
            'Steve Jobs': '苹果公司联合创始人，企业家',
            'Winston Churchill': '英国首相，政治家、作家',
            'Maya Angelou': '美国诗人、作家、民权活动家'
        };
        
        const description = descriptions[author.name];
        if (description) {
            return `
                <p class="author-description">
                    ${description}
                </p>
            `;
        }
        
        return '';
    }

    /**
     * 获取作者详情页URL
     * @param {Object} author - 作者对象
     * @returns {string} 作者详情页URL
     */
    getAuthorUrl(author) {
        if (window.UrlHandler && window.UrlHandler.getAuthorUrl) {
            return window.UrlHandler.getAuthorUrl(author);
        }
        // 降级方案
        return `/authors/${author.id}/`;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        const viewMoreBtn = this.container.querySelector('.view-more-btn');
        if (viewMoreBtn) {
            viewMoreBtn.addEventListener('click', (e) => {
                // 可以在这里添加点击统计等功能
                console.log('AuthorInfoWidget: User clicked view more for author:', this.authorData.name);
            });
        }
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="author-widget">
                <h3 class="widget-title">关于作者</h3>
                <div class="author-summary">
                    <div class="author-avatar-large animate-pulse bg-gray-200 dark:bg-gray-700">
                    </div>
                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
                    <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4 w-3/4"></div>
                    <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
            </div>
        `;
    }

    /**
     * 显示错误状态
     */
    showErrorState() {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="author-widget">
                <h3 class="widget-title">关于作者</h3>
                <div class="author-summary text-center">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-400"></i>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">
                        无法加载作者信息
                    </p>
                </div>
            </div>
        `;
    }

    /**
     * 清空组件
     */
    clear() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        this.authorData = null;
    }
}

// 全局注册组件
window.AuthorInfoWidget = AuthorInfoWidget;
