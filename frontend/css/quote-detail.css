/* 名言详情页专用样式 */

/* ==================== 名言详情卡片 ==================== */
.quote-detail-card {
    @apply relative p-8 bg-gradient-to-br from-yellow-50 to-white 
           dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-lg 
           border border-gray-200 dark:border-gray-700;
    position: relative;
    overflow: hidden;
}

.quote-detail-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
    pointer-events: none;
}

/* ==================== 名言内容区域 ==================== */
.quote-content-area {
    @apply relative z-10 mb-8;
}

.quote-text {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl 
           font-serif leading-relaxed mb-8 text-gray-800 
           dark:text-gray-100;
    font-family: 'Noto Serif', serif;
    position: relative;
    font-weight: 500;
    line-height: 1.4;
}

.quote-text::before,
.quote-text::after {
    content: '"';
    @apply text-yellow-400 absolute;
    font-family: 'Times New Roman', serif;
    font-size: 4rem;
    font-weight: bold;
    opacity: 0.3;
    z-index: -1;
}

.quote-text::before {
    top: -1rem;
    left: -2rem;
}

.quote-text::after {
    bottom: -2rem;
    right: -1rem;
}

/* ==================== 作者信息区域 ==================== */
.quote-attribution {
    @apply relative z-10 mb-6;
}

.author-info {
    @apply flex items-center;
}

.author-avatar {
    @apply w-16 h-16 rounded-full bg-yellow-100 dark:bg-yellow-900 
           flex items-center justify-center text-yellow-600 
           dark:text-yellow-300 border-3 border-yellow-400 
           dark:border-yellow-600 mr-4 flex-shrink-0;
    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.2);
}

.author-details {
    @apply flex-1;
}

.author-name {
    @apply text-xl font-semibold text-yellow-600 dark:text-yellow-400 
           hover:text-yellow-700 dark:hover:text-yellow-300 
           transition-colors duration-300 block mb-1;
    text-decoration: none;
}

.author-name:hover {
    text-decoration: underline;
}

.source-name {
    @apply text-sm text-gray-500 dark:text-gray-400;
    font-style: italic;
}

/* ==================== 分类标签区域 ==================== */
.categories-section {
    @apply flex flex-wrap gap-2 mb-6 relative z-10;
}

.category-tag {
    @apply px-3 py-1 bg-yellow-100 dark:bg-yellow-900 
           text-yellow-800 dark:text-yellow-200 rounded-full 
           text-sm hover:bg-yellow-200 dark:hover:bg-yellow-800 
           transition-all duration-300 cursor-pointer;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.category-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

/* ==================== 操作按钮区域 ==================== */
.quote-actions {
    @apply flex justify-between items-center border-t 
           border-gray-200 dark:border-gray-700 pt-6 mt-8 relative z-10;
}

.quote-meta {
    @apply text-sm text-gray-500 dark:text-gray-400;
}

.action-buttons {
    @apply flex space-x-3;
}

.action-btn {
    @apply flex items-center space-x-2 px-4 py-2 
           text-gray-600 dark:text-gray-400 
           hover:text-yellow-600 dark:hover:text-yellow-400 
           hover:bg-yellow-50 dark:hover:bg-gray-700 
           rounded-lg transition-all duration-300 border
           border-transparent hover:border-yellow-200 
           dark:hover:border-yellow-600;
    min-width: 80px;
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn i {
    @apply text-base;
}

.action-btn.active {
    @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-700 
           dark:text-yellow-300 border-yellow-300 dark:border-yellow-600;
}

/* ==================== 内容网格布局 ==================== */
.content-grid {
    @apply flex flex-col lg:flex-row gap-8;
}

.main-content {
    @apply lg:w-2/3;
}

.sidebar {
    @apply lg:w-1/3;
}

/* ==================== 相关推荐区域 ==================== */
.related-quotes-section {
    @apply mb-8;
}

.section-title {
    @apply text-2xl font-bold mb-6 flex items-center text-gray-800 
           dark:text-gray-100;
}

.related-quotes-list {
    @apply space-y-4;
}

.related-quote-card {
    @apply p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm 
           border border-gray-200 dark:border-gray-700 
           hover:shadow-md transition-all duration-300 cursor-pointer;
}

.related-quote-card:hover {
    transform: translateY(-2px);
    @apply border-yellow-300 dark:border-yellow-600;
}

.related-quote-card .quote-content {
    @apply text-gray-700 dark:text-gray-300 mb-3 leading-relaxed;
    font-size: 1rem;
    line-height: 1.6;
}

.related-quote-card .quote-author {
    @apply text-sm text-yellow-600 dark:text-yellow-400 font-medium;
}

.related-quote-card .quote-card-overlay {
    @apply absolute top-2 right-2 w-8 h-8 bg-yellow-500 text-white
           rounded-full flex items-center justify-center opacity-0
           transition-opacity duration-300;
}

.related-quote-card:hover .quote-card-overlay {
    @apply opacity-100;
}

.related-quote-card {
    @apply relative;
}

/* ==================== 作者信息小部件 ==================== */
.author-widget {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm
           border border-gray-200 dark:border-gray-700 p-6 mb-6;
}

.widget-title {
    @apply text-lg font-semibold mb-4 text-gray-800 dark:text-gray-100;
}

.author-summary {
    @apply text-center;
}

.author-avatar-large {
    @apply w-20 h-20 rounded-full bg-yellow-100 dark:bg-yellow-900
           flex items-center justify-center text-yellow-600
           dark:text-yellow-300 border-3 border-yellow-400
           dark:border-yellow-600 mx-auto mb-4 text-2xl font-bold;
}

.author-stats {
    @apply text-sm text-gray-600 dark:text-gray-400 mb-4;
}

.author-description {
    @apply text-sm text-gray-600 dark:text-gray-400 mb-4 italic;
}

.author-actions {
    @apply mt-4;
}

.view-more-btn {
    @apply inline-flex items-center px-4 py-2 bg-yellow-100
           dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300
           rounded-lg hover:bg-yellow-200 dark:hover:bg-yellow-800
           transition-colors duration-300 text-sm font-medium;
    text-decoration: none;
}

.view-more-btn:hover {
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

/* ==================== 响应式设计 ==================== */

/* 移动端优化 */
@media (max-width: 640px) {
    .quote-text {
        @apply text-xl leading-relaxed;
        font-size: 1.5rem;
        line-height: 1.5;
    }

    .quote-text::before,
    .quote-text::after {
        font-size: 2.5rem;
    }

    .quote-text::before {
        top: -0.5rem;
        left: -1rem;
    }

    .quote-text::after {
        bottom: -1rem;
        right: -0.5rem;
    }

    .author-avatar {
        @apply w-12 h-12 mr-3;
    }

    .author-name {
        @apply text-lg;
    }

    .action-btn span {
        @apply hidden;
    }

    .action-btn {
        @apply px-3 py-2;
        min-width: 44px;
        min-height: 44px; /* 触摸友好的最小尺寸 */
    }

    /* 移动端触摸优化 */
    .category-tag {
        @apply px-4 py-2 text-base;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .related-quote-card {
        @apply p-6;
        min-height: 120px;
    }

    .author-widget {
        @apply p-4;
    }

    .view-more-btn {
        @apply px-6 py-3 text-base;
        min-height: 44px;
    }
}

/* 平板端优化 */
@media (max-width: 768px) {
    .quote-detail-card {
        @apply p-6;
    }

    .content-grid {
        @apply gap-6;
    }

    .quote-text {
        font-size: 1.75rem;
    }

    .section-title {
        @apply text-xl;
    }
}

/* 大屏幕优化 */
@media (min-width: 1024px) {
    .quote-text {
        font-size: 2.5rem;
        line-height: 1.3;
    }

    .quote-detail-card {
        @apply p-12;
    }

    .related-quote-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
}

/* 超大屏幕优化 */
@media (min-width: 1280px) {
    .quote-text {
        font-size: 3rem;
    }

    .quote-detail-card {
        max-width: 1200px;
        margin: 0 auto;
    }
}

/* ==================== 加载状态 ==================== */
.loading-spinner {
    @apply inline-block w-8 h-8 border-4 border-yellow-200 
           border-t-yellow-600 rounded-full animate-spin;
}

/* ==================== 动画效果 ==================== */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-delay-1 {
    animation: fadeIn 0.6s ease-out 0.2s both;
}

.fade-in-delay-2 {
    animation: fadeIn 0.6s ease-out 0.4s both;
}

.fade-in-delay-3 {
    animation: fadeIn 0.6s ease-out 0.6s both;
}

/* ==================== 深色模式优化 ==================== */
.dark-mode .quote-detail-card {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(17, 24, 39, 0.98) 100%);
}

.dark-mode .related-quote-card {
    @apply bg-gray-800 border-gray-600;
}

.dark-mode .author-widget {
    @apply bg-gray-800 border-gray-600;
}
